// ===== Generic Use/View/Collect engine (id-based) =====
const GRACE_MS = 1200; // keep markers virtually-visible briefly after loss

const Engine = {
  // marker state
  virtualVisible: new Set(),      // respects grace period
  trulyVisible: new Set(),        // raw found/lost
  lossTimers: {},                 // markerValue -> timeout id

  // inventory (single slot)
  collectedEl: null,              // the 3D container we moved (usually .collectable-model)
  collectedId: null,              // current id of collected item ("bucket", "water_bucket", ...)
  inventoryAnchor: null           // where collected item sits (top-of-screen)
};

// ------- UI helpers -------
function showToast(text, ms = 2600) {
  const wrap = document.querySelector('.toasts');
  const t = document.createElement('div');
  t.className = 'toast';
  t.textContent = text;
  wrap.appendChild(t);
  setTimeout(() => t.remove(), ms);
}
function setChip(id, on) {
  const el = document.getElementById(id);
  if (el) el.style.display = on ? 'flex' : 'none';
}
function updateInventoryLabel() {
  const box = document.getElementById('invLabel');
  const name = document.getElementById('invName');
  if (!box || !name) return;
  if (Engine.collectedId) {
    name.textContent = Engine.collectedId;
    box.style.display = 'flex';
  } else {
    box.style.display = 'none';
  }
}
const anyVisible = () => Engine.virtualVisible.size > 0;

// ------- HUD binding (click + touch, stop propagation) -------
function bindHUD(sys) {
  const map = [
    ['btn-view',   () => sys.onView()],
    ['btn-use',    () => sys.onUse()],
    ['btn-collect',() => sys.onCollect()]
  ];
  for (const [id, fn] of map) {
    const el = document.getElementById(id);
    if (!el) { console.warn('[game] HUD button missing:', id); continue; }
    if (el.dataset.bound === '1') continue; // avoid double binding

    const handler = (e) => {
      e.preventDefault();
      e.stopPropagation();
      fn();
    };

    el.addEventListener('click', handler, {passive:false});
    el.addEventListener('touchstart', handler, {passive:false});

    el.dataset.bound = '1';
  }
}

// ------- A-Frame system -------
AFRAME.registerSystem('game', {
  init() {
    this.itemsByMarker = {}; // value -> { markerEl, itemEl, activeMetaName, metas }
    this.scene = this.el;

    // track last-known visibility to synthesize found/lost in tick()
    this._prevVis = {}; // value -> boolean

    // Mouse raycaster for clicking collected 3D item
    const cursor = document.createElement('a-entity');
    cursor.setAttribute('cursor', 'rayOrigin: mouse');
    cursor.setAttribute('raycaster', 'objects: .clickable');
    this.scene.appendChild(cursor);

    // Bind HUD immediately (script is defer'd, DOM is ready)
    bindHUD(this);
  },

  // Register a marker + item entity (itemEl has item-meta)
  registerItem(markerValue, markerEl, itemEl) {
    const metas = this.readMetas(itemEl);
    const activeMetaName = 'base';

    // Ensure current meta has an id
    if (!metas[activeMetaName] || !stringTrim(metas[activeMetaName].id)) {
      console.warn(`[game] item on marker ${markerValue} missing item-meta id; defaulting to "item${markerValue}"`);
      metas[activeMetaName] = metas[activeMetaName] || {};
      metas[activeMetaName].id = `item${markerValue}`;
    }

    this.itemsByMarker[markerValue] = { markerEl, itemEl, metas, activeMetaName };

    // Apply initial variant
    this.applyVariantForItem(itemEl, metas[activeMetaName].id);

    // Listen to AR.js events (if emitted)
    const onFound = () => this._markFound(markerValue);
    const onLost  = () => this._markLost(markerValue);
    markerEl.addEventListener('markerFound', onFound);
    markerEl.addEventListener('markerLost',  onLost);
    this.el.addEventListener('markerFound', (evt) => { if (evt.target === markerEl) onFound(); }, true);
    this.el.addEventListener('markerLost',  (evt) => { if (evt.target === markerEl) onLost();  }, true);

    // Initialize prevVis so tick() can synthesize transitions
    this._prevVis[markerValue] = !!(markerEl.object3D && markerEl.object3D.visible);
    if (this._prevVis[markerValue]) this._markFound(markerValue);
  },

  // Synthesize found/lost by polling object3D.visible every frame
  tick() {
    for (const [valueStr, rec] of Object.entries(this.itemsByMarker)) {
      const v = +valueStr;
      const m = rec.markerEl;
      const now = !!(m && m.object3D && m.object3D.visible);
      const prev = !!this._prevVis[v];
      if (now && !prev) this._markFound(v);
      if (!now && prev) this._markLost(v);
      this._prevVis[v] = now;
    }
  },

  _markFound(v) {
    clearTimeout(Engine.lossTimers[v]);
    if (!Engine.trulyVisible.has(v)) Engine.trulyVisible.add(v);
    if (!Engine.virtualVisible.has(v)) {
      Engine.virtualVisible.add(v);
      this.updateChips(v, true);
    }
  },

  _markLost(v) {
    if (Engine.trulyVisible.has(v)) Engine.trulyVisible.delete(v);
    clearTimeout(Engine.lossTimers[v]);
    Engine.lossTimers[v] = setTimeout(() => {
      if (Engine.virtualVisible.has(v)) {
        Engine.virtualVisible.delete(v);
        this.updateChips(v, false);
      }
    }, GRACE_MS);
  },

  setInventoryAnchor(el) { Engine.inventoryAnchor = el; },

  updateChips(markerValue, isOn) {
    const chipId = `chip${markerValue}`;
    setChip(chipId, isOn);
  },

  // ---- VERB: VIEW ----
  onView() {
    let showed = false;
    for (const v of Engine.virtualVisible) {
      const s = this.itemsByMarker[v];
      if (!s) continue;
      const meta = s.metas[s.activeMetaName];
      if (meta.view) { showToast(meta.view); showed = true; }
    }
    if (!showed) showToast('Point at an item and press View.');
  },

  // ---- VERB: USE ----
  onUse() {
    // If we have something collected, try to use it on visible target(s)
    if (Engine.collectedEl && Engine.collectedId) {
      for (const v of Engine.virtualVisible) {
        const s = this.itemsByMarker[v];
        if (!s) continue;
        const meta = s.metas[s.activeMetaName];

        // Match by required id (supports comma-separated list)
        if (matchesNeeded(meta.needs_collected, Engine.collectedId)) {
          // Success!
          const msg = meta.use_success || 'Success.';
          showToast(msg);

          // Target meta change (e.g., flower -> grown_flower)
          const changeTo = stringTrim(meta.change);
          if (changeTo && s.metas[changeTo]) {
            s.activeMetaName = changeTo;
            const newMeta = s.metas[changeTo];
            if (stringTrim(newMeta.id)) this.applyVariantForItem(s.itemEl, newMeta.id);
          }

          // Transform the collected item (e.g., bucket -> water_bucket)
          const transformTo = stringTrim(meta.transform_collected_to);
          if (transformTo) {
            Engine.collectedId = transformTo;
            this.applyVariantForCollected(Engine.collectedEl, transformTo);
            updateInventoryLabel();
          }

          // Optional consumption (e.g., giving flower to Mom)
          if (truthy(meta.consume_collected)) {
            Engine.collectedEl.setAttribute('visible', false);
            if (Engine.collectedEl.object3D) Engine.collectedEl.object3D.visible = false;
            Engine.collectedEl = null;
            Engine.collectedId = null;
            updateInventoryLabel();
          }
          return;
        }
      }

      // We had an item but no valid target in view
      showToast('On what?');
      return;
    }

    // No item collected: using on visible items shows their "use" prompt
    if (anyVisible()) {
      let showed = false;
      for (const v of Engine.virtualVisible) {
        const s = this.itemsByMarker[v];
        if (!s) continue;
        const meta = s.metas[s.activeMetaName];
        if (meta.use) { showToast(meta.use); showed = true; }
      }
      if (!showed) showToast('Nothing to use right now.');
    } else {
      showToast('Nothing to use right now.');
    }
  },

  // ---- VERB: COLLECT ----
  onCollect() {
    // Try to collect any visible item with collect=true
    for (const v of Engine.virtualVisible) {
      const s = this.itemsByMarker[v];
      if (!s) continue;
      const meta = s.metas[s.activeMetaName];

      const collectVal = stringTrim(meta.collect);
      if (collectVal === 'true') {
        return this.collectItem(s.itemEl, meta.id);
      }
      if (collectVal && collectVal !== 'false') {
        return showToast(collectVal); // reason why not
      }
    }
    showToast('No item in view to collect.');
  },

  collectItem(itemEl, itemId) {
    // Move the designated collectable container (must exist for collectable items)
    let model = itemEl.querySelector('.collectable-model');
    if (!model) { showToast('This item has no collectable-model.'); return; }

    const anchor = Engine.inventoryAnchor || document.querySelector('[camera]');
    if (!anchor) return;

    // Re-parent to inventory anchor
    if (model.parentNode !== anchor) anchor.appendChild(model);

    // Place it top-center, slightly forward
    model.setAttribute('position', '0 0 0'); // anchor already positioned in front of cam
    model.setAttribute('rotation', '0 0 0');
    model.setAttribute('scale', '0.65 0.65 0.65');
    model.setAttribute('visible', true);
    if (model.object3D) model.object3D.visible = true;
    model.classList.add('clickable');

    // Clicking the collected model attempts a use
    model.onclick = () => this.onUse();

    // Record current collected id and ensure correct variant is shown
    Engine.collectedEl = model;
    Engine.collectedId = itemId || '';
    if (Engine.collectedId) this.applyVariantForCollected(model, Engine.collectedId);

    updateInventoryLabel();
    showToast('Collected.');
  },

  // ---- Meta reading / switching ----
  readMetas(itemEl) {
    // Base meta from component (parsed by A-Frame)
    const base = itemEl.getAttribute('item-meta') || {};

    // Multi-instance syntax preferred: item-meta__name="..."
    const metas = { base };
    // Also accept item-meta__* attributes
    for (const attr of itemEl.attributes) {
      if (attr.name.startsWith('item-meta__')) {
        const name = attr.name.split('__')[1];
        const obj = itemEl.getAttribute(attr.name) || parseKV(attr.value);
        metas[name] = obj;
      }
      // Fallback: item-meta2="..." style
      if (/^item-meta\d+$/.test(attr.name)) {
        metas[attr.name.replace('item-meta','meta')] = parseKV(attr.value);
      }
    }
    return metas;
  },

  // Toggle which variant is visible for an ITEM (in-scene)
  applyVariantForItem(itemEl, id) {
    const nodes = itemEl.querySelectorAll('[variant]');
    if (!nodes.length) return;
    nodes.forEach(n => n.setAttribute('visible', n.getAttribute('variant') === id));
  },

  // Toggle which variant is visible for the COLLECTED model (in inventory)
  applyVariantForCollected(modelEl, id) {
    const nodes = modelEl.querySelectorAll('[variant]');
    if (!nodes.length) return;
    nodes.forEach(n => n.setAttribute('visible', n.getAttribute('variant') === id));
  }
});

// ------- Component to carry item behavior -------
// multiple:true lets you declare item-meta__meta2="..."
AFRAME.registerComponent('item-meta', {
  multiple: true,
  schema: {
    id: {type:'string', default:''},                 // logical item id (bucket, water_bucket, flower, grown_flower, mom, well)
    view: {type:'string', default:''},
    use: {type:'string', default:''},
    collect: {type:'string', default:''},            // "true" / "false" / reason string
    needs_collected: {type:'string', default:''},    // required collected id(s), comma-separated
    use_success: {type:'string', default:''},        // shown on success
    change: {type:'string', default:''},             // switch this item's meta to another instance name (e.g., meta2)
    transform_collected_to: {type:'string', default:''}, // change the collected id (e.g., bucket -> water_bucket)
    consume_collected: {type:'string', default:''}   // "true" to remove collected on success (default false)
  }
});

// ------- Wire up once the scene is ready -------
AFRAME.registerComponent('wire-up', {
  init() {
    const sys = this.el.sceneEl.systems['game'];
    const doRegister = () => {
      [1,2,3,4].forEach(v => {
        const marker = document.querySelector(`a-marker[type="barcode"][value="${v}"]`);
        const item = marker ? marker.querySelector('[item-meta]') : null;
        if (marker && item) sys.registerItem(v, marker, item);
      });

      // Inventory anchor (top of screen, child of camera)
      const cam = document.querySelector('[camera]');
      const inv = document.createElement('a-entity');
      inv.setAttribute('position', '0 0.35 -0.7'); // higher/farther so it's always visible
      cam.appendChild(inv);
      sys.setInventoryAnchor(inv);

      // (Re)bind HUD — idempotent
      bindHUD(sys);
    };

    if (this.el.hasLoaded) doRegister();
    else this.el.addEventListener('loaded', doRegister);
  }
});

// ------- Small helpers -------
function stringTrim(v){ return (v || '').toString().trim(); }
function truthy(v){ return stringTrim(v) === 'true'; }
function parseCSV(s){
  return stringTrim(s).split(',').map(x=>x.trim()).filter(Boolean);
}
function matchesNeeded(needs, haveId){
  if (!haveId) return false;
  const list = parseCSV(needs);
  if (!list.length) return false;
  return list.includes(haveId);
}

// Fallback manual parser for "a: 1; b: two;" style strings
function parseKV(s){
  const out = {};
  if (!s) return out;
  s.split(';').forEach(pair=>{
    const i = pair.indexOf(':');
    if (i>-1){
      const k = pair.slice(0,i).trim();
      const v = pair.slice(i+1).trim();
      if (k) out[k] = v;
    }
  });
  return out;
}
