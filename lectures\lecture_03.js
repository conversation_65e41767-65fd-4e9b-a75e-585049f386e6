// Safe broadcast: only emit on A-Frame entities (elements that actually have .emit)
    function broadcast(root, name, detail){
      root.querySelectorAll('*').forEach(el => {
        if (el && typeof el.emit === 'function') el.emit(name, detail, false);
      });
    }

    // Tracks visible barcode markers -> builds combo key (e.g. "24+26")
    AFRAME.registerComponent('combo-controller', {
      init(){
        this.active = new Set();
        this.lastKey = '';
        this.hud = document.getElementById('hud');

        const markers = Array.from(document.querySelectorAll('a-marker[type="barcode"]'));
        markers.forEach(m=>{
          const id = String(m.getAttribute('value'));
          m.addEventListener('markerFound', ()=>{ this.active.add(id); this.updateCombo(); });
          m.addEventListener('markerLost',  ()=>{ this.active.delete(id); this.updateCombo(); });
        });
      },
      key(){
        if (!this.active.size) return '';
        return Array.from(this.active).sort((a,b)=>(+a)-(+b)).join('+');
      },
      updateCombo(){
        const k = this.key();
        if (this.hud) this.hud.textContent = k ? `Active: ${k}` : 'No markers';

        // stop previous combo animations
        broadcast(this.el, 'combo-clear', {});
        // start new combo on next tick
        if (k) setTimeout(()=>broadcast(this.el, `combo-${k}`, {active:[...this.active]}), 0);

        this.lastKey = k;
      }
    });