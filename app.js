// --- DOM refs ---------------------------------------------------------------
let editor;
const iframe   = document.getElementById('preview');
const runBtn   = document.getElementById('runBtn');
const resetBtn = document.getElementById('resetBtn');
const autoRun  = document.getElementById('autoRun');

// --- Helpers ----------------------------------------------------------------
function setEditorMode(mode){ if (editor?.setOption) editor.setOption('mode', mode); }
function detectHTML(code){ return /<\s*(!doctype|html|a-scene)\b/i.test(code); }
function storageKeyFor(key){ return `code:${key}`; }
function currentKey(){ return localStorage.getItem('currentLecture'); }
function setCurrentKey(k){ localStorage.setItem('currentLecture', k); }

// Remove the Live Server injected inline <script> block (no src attribute)
function stripLiveServerInjection(html){
  // Remove any inline <script> that contains telltale tokens and NO src=
  const rx = /<script(?![^>]*\bsrc=)[^>]*>[\s\S]*?(LiveServer|_cacheOverride|\/ws|Live reload)[\s\S]*?<\/script>\s*/gi;
  const cleaned = html.replace(rx, '');
  // Also remove the leading marker comment if present
  return cleaned.replace(/<!--\s*Code injected by live-server\s*-->\s*/gi, '');
}

async function fetchText(url){
  const abs = new URL(url, document.baseURI).href;
  const res = await fetch(abs, { cache: 'no-store' });
  if (!res.ok) throw new Error(`${res.status} ${res.statusText}`);
  return await res.text();
}

// --- Editor -----------------------------------------------------------------
function createEditor(){
  editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
    mode: 'htmlmixed',
    theme: 'material-darker',
    lineNumbers: true,
    lineWrapping: true,
    tabSize: 2,
    indentUnit: 2,
    autofocus: true,
  });
  setTimeout(() => editor.refresh(), 0);

  editor.on('change', () => {
    if (autoRun.checked) scheduleRun();
    saveCurrentCode();
  });

  editor.addKeyMap({ 'Ctrl-Enter': run, 'Cmd-Enter': run });
}

// --- Sidebar / Lectures -----------------------------------------------------
function wireSidebar(){
  // Toggle sections
  document.querySelectorAll('.lecture-header').forEach(header => {
    header.addEventListener('click', (e) => {
      const isArrow = e.target.classList.contains('arrow');
      const alt = e.altKey;

      if (isArrow && alt) {
        // Collapse all
        document.querySelectorAll('.lecture-header').forEach(h => {
          h.setAttribute('aria-expanded', 'false');
          const items = h.nextElementSibling;
          if (items) items.style.display = 'none';
        });
        return;
      }

      const expanded = header.getAttribute('aria-expanded') !== 'false';
      header.setAttribute('aria-expanded', expanded ? 'false' : 'true');
      const items = header.nextElementSibling;
      if (items) items.style.display = expanded ? 'none' : '';
    });
  });

  // Click items
  document.querySelectorAll('.lecture-item').forEach(btn => {
    btn.addEventListener('click', () => selectLecture(btn));
  });

  // Ensure only one active item at a time (even if HTML had multiple .active)
  const all = [...document.querySelectorAll('.lecture-item')];
  const firstActive = all.find(b => b.classList.contains('active')) || all[0];
  all.forEach(b => b.classList.remove('active'));
  if (firstActive) firstActive.classList.add('active');
}

async function selectLecture(btn){
  // Visually select
  document.querySelectorAll('.lecture-item').forEach(b => b.classList.remove('active'));
  btn.classList.add('active');

  const key = btn.getAttribute('data-key');
  const src = btn.getAttribute('data-src');
  setCurrentKey(key);

  // Prefer saved code
  const saved = localStorage.getItem(storageKeyFor(key));
  if (saved){
    editor.setValue(saved);
    setEditorMode('htmlmixed');
    run();
    return;
  }

  try {
    const raw = await fetchText(src);
    const code = stripLiveServerInjection(raw);
    editor.setValue(code);
    setEditorMode('htmlmixed');
    run();
  } catch (err){
    editor.setValue(`<!-- Failed to load ${src}\n${String(err)} -->`);
    setEditorMode('htmlmixed');
    run();
  }
}

// --- Run / Preview ----------------------------------------------------------
let runTimer;
function scheduleRun(){ clearTimeout(runTimer); runTimer = setTimeout(run, 200); }

function run(){
  const code = editor.getValue();
  if (detectHTML(code)){
    iframe.setAttribute('srcdoc', code);
  } else {
    iframe.setAttribute('srcdoc', `<pre style="padding:16px;font:14px/1.4 system-ui">Unsupported content</pre>`);
  }
}

// --- Persistence ------------------------------------------------------------
function saveCurrentCode(){
  const key = currentKey() || (document.querySelector('.lecture-item')?.dataset.key);
  if (!key) return;
  localStorage.setItem(storageKeyFor(key), editor.getValue());
}

async function resetCurrentLecture(){
  const key = currentKey() || (document.querySelector('.lecture-item')?.dataset.key);
  const btn = key && document.querySelector(`.lecture-item[data-key="${key}"]`);
  if (!btn) return;
  const src = btn.getAttribute('data-src');
  try {
    const raw = await fetchText(src);
    const code = stripLiveServerInjection(raw);
    localStorage.removeItem(storageKeyFor(key));
    editor.setValue(code);
    setEditorMode('htmlmixed');
    run();
  } catch (err){
    editor.setValue(`<!-- Failed to reload ${src}\n${String(err)} -->`);
    run();
  }
}

// --- Toolbar ----------------------------------------------------------------
function wireToolbar(){
  runBtn.addEventListener('click', run);
  resetBtn.addEventListener('click', resetCurrentLecture);
}

// --- Init -------------------------------------------------------------------
window.addEventListener('DOMContentLoaded', async () => {
  createEditor();
  wireSidebar();
  wireToolbar();

  // Pick last-open lecture or first
  const key = currentKey();
  let btn = key ? document.querySelector(`.lecture-item[data-key="${key}"]`) : null;
  if (!btn) btn = document.querySelector('.lecture-item');
  if (btn) await selectLecture(btn);
});
