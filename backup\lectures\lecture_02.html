<html>
<head>
    <meta charset="UTF-8">
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
</head>
<body>
    <a-scene
        embedded
        arjs="sourceType: webcam; debugUIEnabled: false;
                detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
                imageProcessing: {
                thresholdingBlockSize: 25,
                thresholdingConstant: -9
                }"
        vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true">

        <a-marker type="barcode" value="24">
            <a-cylinder
            position="0 0.5 0"
            radius="0.3"
            height="1"
            material="color: blue;"
            animation="
                property: scale;
                to: 1 2 1;
                loop: true;
                dur: 1000;"
            ></a-cylinder>
        </a-marker>

        <a-marker type="barcode" value="25">
            <a-box
            position="0 0.5 0"
            scale=".5 .5 .5"
            material="color: green;"
            animation="
                property: rotation;
                to: 0 360 0;
                loop: true;
                easing: linear;
                dur: 1000;"
            ></a-box>
        </a-marker>

        <a-marker type="barcode" value="26">
          <a-torus position="0 0.5 0"
                   color="orange"
                   radius="1"
                   scale=".5 .5 .5"
                   animation__1="property: components.material.material.color;
                              type: color;
                              to: tomato;
                              dur: 500;
                              dir: alternate;
                              easing: easeOutQuad;
                              loop: true"
                   animation__2="property: position;
                                 to: 0 1.5 0;
                                 dur: 500;
                                 dir: alternate;
                                 easing: easeOutElastic;
                                 loop: true"
                   animation__3="property: rotation;
                                 to: 0 180 0;
                                 dur: 500;
                                 dir: alternate;
                                 easing: easeInSine;
                                 loop: true"
           ></a-torus>
        </a-marker>

        <a-entity camera></a-entity>
    </a-scene>
</body>
</html>