<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover" />

  <!-- A-Frame + AR.js -->
  <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/aframe-extras@6.1.1/dist/aframe-extras.animation-mixer.min.js"></script>
  <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>
  <link rel="stylesheet" href="style.css" />
  <script defer src="script.js"></script>
</head>
<body>

  <div class="toasts" aria-live="polite"></div>

  <div class="chips">
    <div id="chip1" class="chip" style="display:none;"><span class="dot"></span> KEY (1)</div>
    <div id="chip2" class="chip" style="display:none;"><span class="dot"></span> DOOR (2)</div>
    <div id="chip3" class="chip" style="display:none;"><span class="dot"></span> 🪣 BUCKET (3)</div>
    <div id="chip4" class="chip" style="display:none;"><span class="dot"></span> 💧 WELL (4)</div>
  </div>

  <div id="invLabel" class="inventory-label" style="display:none;">
    <span class="ic">📦</span>
    <span>Collected:</span>
    <strong id="invName">—</strong>
  </div>

  <div class="hud">
    <button id="btn-view" class="btn" aria-label="View"><span class="ic">👁</span><span>View</span></button>
    <button id="btn-use" class="btn" aria-label="Use"><span class="ic">🛠</span><span>Use</span></button>
    <button id="btn-collect" class="btn" aria-label="Collect"><span class="ic">🧺</span><span>Collect</span></button>
  </div>

  <a-scene embedded
    arjs="sourceType: webcam; debugUIEnabled: false;
          detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
          sourceWidth:1280; sourceHeight:960; displayWidth:1280; displayHeight:960;
          imageProcessing: { thresholdingBlockSize: 25, thresholdingConstant: -9 }"
    vr-mode-ui="enabled: false"
    renderer="logarithmicDepthBuffer: true"
    wire-up
  >

    <a-assets timeout="2000">
      <a-asset-item id="doorModel" src="door.glb"></a-asset-item>
    </a-assets>

    <!-- Marker: A (collect) -->
    <a-marker type="barcode" value="1">
      <a-entity
        item-meta="
          id: A;
          view: This is A.;
          use: Does nothing.;
          collect: true;
        "
      >
        <!-- Visual for the A -->
        <a-entity variant="keyA" class="collectable-model">
          <a-text value="A" position="0 0.05 0"></a-box>
        </a-entity>
      </a-entity>
    </a-marker>

        <!-- Marker: B (collect) -->
    <a-marker type="barcode" value="2">
      <a-entity
        item-meta="
          id: B;
          view: This is B.;
          use: Does nothing.;
          collect: true;
        "
      >
        <!-- Visual for the B -->
        <a-entity variant="keyB" class="collectable-model">
          <a-text value="B" position="0 0.05 0"></a-box>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- Marker: C (collect) -->
    <a-marker type="barcode" value="3">
      <a-entity
        item-meta="
          id: C;
          view: This is C.;
          use: Does nothing.;
          collect: true;
          needs_collected: ;
          use_success: ;
        "
      >
        <!-- Visual for the C -->
        <a-entity variant="keyC" class="collectable-model">
          <a-text value="C" position="0 0.05 0"></a-box>
        </a-entity>
      </a-entity>
    </a-marker>

<!-- Marker: DOOR (needs key; opens on success; consumes key) -->
    <a-marker type="barcode" value="4">
      <!-- The main item-meta should be on the parent that holds the variants -->
      <a-entity
        item-meta="
          id: book;
          view: An empty book.;
          use: It's empty.;
          collect: false;
          needs_collected: keyA, keyB, keyC;
          use_success: This is an ABC book.;
          change: new_book;             
          consume_collected: true;     
        "
        item-meta__door_open="    
          id: new_book;
          view: This is an ABC book.;
          use: Learn your ABC's.;
          collect: false;
        "
      >
        <!-- CLOSED variant (no animation) -->
        <a-entity variant="book">
          <a-text value="X" position="0 0.05 0"></a-entity>
        </a-entity>

        <!-- OPEN variant (plays once when variant becomes active) -->
        <a-entity variant="new_book">
          <a-text value="ABC" position="0 0.05 0"></a-text>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- Camera -->
    <a-entity camera></a-entity>
  </a-scene>
</body>
</html>
