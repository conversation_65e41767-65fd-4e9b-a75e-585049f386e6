<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>AR Use / View / Collect — ID-based</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover">
  <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>
  <link rel="stylesheet" href="https://veilands.lv/a-frame/point-and-click/style.css" />
  <script defer src="https://veilands.lv/a-frame/point-and-click/script.js"></script>
</head>
<body>
  <div class="toasts" aria-live="polite"></div>

  <div class="chips">
    <div id="chip1" class="chip" style="display:none;"><span class="dot"></span> 👩 MOM (1)</div>
    <div id="chip2" class="chip" style="display:none;"><span class="dot"></span> 🌸 FLOWER (2)</div>
    <div id="chip3" class="chip" style="display:none;"><span class="dot"></span> 🪣 BUCKET (3)</div>
    <div id="chip4" class="chip" style="display:none;"><span class="dot"></span> 💧 WELL (4)</div>
  </div>

  <div id="invLabel" class="inventory-label" style="display:none;">
    <span class="ic">📦</span>
    <span>Collected:</span>
    <strong id="invName">—</strong>
  </div>

  <div class="hud">
    <button id="btn-view" class="btn" aria-label="View"><span class="ic">👁</span><span>View</span></button>
    <button id="btn-use" class="btn" aria-label="Use"><span class="ic">🛠</span><span>Use</span></button>
    <button id="btn-collect" class="btn" aria-label="Collect"><span class="ic">🧺</span><span>Collect</span></button>
  </div>

  <!-- AR Scene -->
  <a-scene embedded
    arjs="sourceType: webcam; debugUIEnabled: false;
          detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
          sourceWidth:1280; sourceHeight:960; displayWidth:1280; displayHeight:960;
          imageProcessing: { thresholdingBlockSize: 25, thresholdingConstant: -9 }"
    vr-mode-ui="enabled: false"
    renderer="logarithmicDepthBuffer: true"
    wire-up
  >

    <a-entity camera></a-entity>
  </a-scene>
</body>
</html>
