<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>A-Frame Point & Click Engine</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
  <link rel="stylesheet" href="styles.css">
  <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
  <script src="js/pointclick-engine.js" defer></script>
</head>
<body>
  <div id="rotateNotice" style="display:none">Please rotate to landscape.</div>
  <div id="inventory">
    <div class="slot" data-slot="0"></div>
    <div class="slot" data-slot="1"></div>
    <div class="slot" data-slot="2"></div>
    <div class="slot" data-slot="3"></div>
  </div>

  <a-scene background="color: #000">
    <a-assets id="assets">
      <!-- (Optional) GLB rooms: keep these if you want real models -->
      <!-- <a-asset-item id="room-a" src="assets/room-a.glb"></a-asset-item> -->
      <!-- <a-asset-item id="room-b" src="assets/room-b.glb"></a-asset-item> -->
      <!-- <a-asset-item id="room-c" src="assets/room-c.glb"></a-asset-item> -->

      <!-- Built-in prototypes: DO NOT use <a-asset-item> without src -->
      <!-- Doors -->
      <a-entity id="door-a-to-b" data-proto="door" room="a" door="room-b" pos="0.7 0 -0.3"></a-entity>
      <a-entity id="door-b-to-a" data-proto="door" room="b" door="room-a" pos="-0.7 0 -0.3"></a-entity>
      <a-entity id="door-b-locked" data-proto="door" room="b" door="room-c" pos="0.7 0 -0.3" connect="door-a"></a-entity>
      <a-entity id="door-c-back" data-proto="door" room="c" door="room-b" pos="-0.7 0 -0.3"></a-entity>

      <!-- Item -->
      <a-entity id="key-a" data-proto="item" room="a" pickable connect="door-a" icon="icons/key.png" pos="0 0 -0.1"
        animation__hover="property: rotation; to: 0 360 0; loop: true; dur: 2500"></a-entity>
    </a-assets>

    <a-entity id="game"
        pointclick-engine="mapSrc: maps/level1.txt; startRoom: a; blockWidth: 1.6; blockDepth: 0.9; moveMs: 2000; view: panel; viewAxis: z;">
    </a-entity>

    <a-entity id="rig" position="0 0 0">
      <a-entity id="camera" camera look-controls="enabled:false" wasd-controls="enabled:false"
        cursor="rayOrigin: mouse" raycaster="objects: .pc-interactive"></a-entity>
    </a-entity>

    <a-entity light="type: ambient; intensity: 0.8"></a-entity>
  </a-scene>
</body>
</html>