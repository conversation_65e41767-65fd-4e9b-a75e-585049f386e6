<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>A-Frame GLB Animation Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- A-Frame core -->
    <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>

    <!-- Animation Mixer (from aframe-extras) -->
<script src="https://cdn.jsdelivr.net/npm/aframe-extras@6.1.1/dist/aframe-extras.animation-mixer.min.js"></script>
  </head>

  <body>
    <a-scene background="color: #ECECEC">

      <!-- Preload your model -->
      <a-assets>
        <a-asset-item id="doorModel" src="door.glb"></a-asset-item>
      </a-assets>

      <!-- Model entity with animation -->
      <a-entity
        gltf-model="#doorModel"
  animation-mixer="loop: once; clampWhenFinished: true"
        position="0 0 -3"
        scale="1 1 1">
      </a-entity>

      <!-- Basic camera + light -->
      <a-entity camera position="0 1.6 0"></a-entity>
      <a-light type="ambient" intensity="0.7"></a-light>
      <a-light type="directional" position="1 2 1" intensity="1"></a-light>

    </a-scene>
    <script>
  const door = document.querySelector('#door');
  door.addEventListener('model-loaded', () => {
    // Force animation mixer restart
    const cfg = door.getAttribute('animation-mixer');
    door.removeAttribute('animation-mixer');
    setTimeout(() => door.setAttribute('animation-mixer', cfg), 100);
  });
</script>
  </body>
</html>
