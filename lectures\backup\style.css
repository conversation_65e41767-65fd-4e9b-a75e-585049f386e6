html, body { margin:0; height:100%; overflow:hidden; font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; }

/* 2× sizing for the HUD & toasts */
:root{
  --btn-font: 28px;
  --btn-pad-v: 20px;
  --btn-pad-h: 28px;
  --toast-font: 28px;
  --chip-font: 22px;
}

.hud {
  position: fixed; right: 14px; bottom: 14px;
  display: flex; gap: 14px; z-index: 9999; pointer-events: auto;
}
.btn {
  display: inline-flex; align-items: center; gap: 12px;
  padding: var(--btn-pad-v) var(--btn-pad-h);
  font-size: var(--btn-font);
  border-radius: 16px;
  background: rgba(20,20,20,.86); color: #fff;
  border: 1px solid rgba(255,255,255,.28);
  cursor: pointer; user-select: none;
  backdrop-filter: blur(6px);
}
.btn:active { transform: translateY(1px); }
.btn .ic { font-size: calc(var(--btn-font) + 4px); }

.toasts {
  position: fixed; left: 50%; transform: translateX(-50%);
  top: 16px; display: flex; flex-direction: column; gap: 12px;
  z-index: 10000; width: min(92vw, 800px); pointer-events: none;
}
.toast {
  padding: 14px 18px; background: rgba(0,0,0,.82); color: #fff;
  border-radius: 14px; line-height: 1.25;
  border: 1px solid rgba(255,255,255,.24); text-align: center;
  font-size: var(--toast-font);
  box-shadow: 0 12px 32px rgba(0,0,0,.35);
}

.chips {
  position: fixed; left: 14px; bottom: 14px; display: flex;
  flex-direction: column; gap: 10px; z-index: 9998; pointer-events: none;
}
.chip {
  display: inline-flex; align-items: center; gap: 8px;
  padding: 8px 12px; border-radius: 999px;
  background: rgba(0,0,0,.64); color:#fff;
  border: 1px solid rgba(255,255,255,.24);
  font-size: var(--chip-font);
}
.chip .dot { width: 10px; height: 10px; border-radius: 50%; background: #6cf; }

.inventory-label{
  position: fixed; left: 50%; transform: translateX(-50%);
  top: 70px; z-index: 9999;
  background: rgba(0,0,0,.65); color:#fff;
  border: 1px solid rgba(255,255,255,.28);
  padding: 8px 12px; border-radius: 12px;
  font-size: 18px; display: flex; gap: 8px; align-items: center;
}
.inventory-label .ic{ font-size: 20px; }

.inventory-bar{
  position: fixed;
  top: 12px; left: 50%; transform: translateX(-50%);
  display: flex; gap: 10px; z-index: 10001;
  background: rgba(0,0,0,.55);
  border: 1px solid rgba(255,255,255,.24);
  padding: 6px 10px; border-radius: 12px;
  pointer-events: auto;
}
.inventory-bar .inv-icon{
  display: inline-flex; align-items: center; gap: 6px;
  padding: 6px 10px; border-radius: 999px;
  background: rgba(0,0,0,.66); color:#fff; font-size: 16px;
  border: 1px solid rgba(255,255,255,.22);
  cursor: pointer; user-select: none;
}
.inventory-bar .inv-icon .ic{ font-size: 18px; }

.foothelp {
  position: fixed; left: 50%; transform: translateX(-50%);
  bottom: 86px; color:#fff; opacity:.95;
  background: rgba(0,0,0,.50); border: 1px solid rgba(255,255,255,.22);
  padding: 8px 12px; border-radius: 10px; z-index: 9998; font-size: 16px;
}

/* Make sure the AR canvas sits below UI (z-index managed by browser stacking) */
a-scene canvas { z-index: 1; }
