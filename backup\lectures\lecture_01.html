<html>
<head>
    <meta charset="UTF-8">
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
</head>
<body>
    <a-scene
        embedded
        arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5; debugUIEnabled: false;"
        vr-mode-ui="enabled: false;"
        renderer="logarithmicDepthBuffer: true;"
    >

        <a-marker type="barcode" value="24">
            <a-cylinder
            position="0 0.5 0"
            radius="0.3" height="1"
            material="color: blue;"
            ></a-cylinder>
            <a-text
            value="BARCODE 24"
            position="0 1.5 0"
            align="center"
            ></a-text>
        </a-marker>

        <a-marker type="barcode" value="25">
            <a-sphere
            position="0 0.5 0"
            radius="0.5"
            material="color: green;"
            ></a-sphere>
            <a-text
            value="BARCODE 25"
            position="0 1.5 0"
            align="center"></a-text>
        </a-marker>

        <a-marker type="barcode" value="26">
            <a-cone position="0 0.5 0"
            radius-bottom="0.5"
            height="1"
            material="color: yellow;"
            ></a-cone>
            <a-text
            value="BARCODE 26"
            position="0 1.5 0"
            align="center"
            ></a-text>
        </a-marker>

        <a-entity camera></a-entity>
    </a-scene>
</body>
</html>