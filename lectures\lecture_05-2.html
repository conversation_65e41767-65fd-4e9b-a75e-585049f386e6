<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover" />

  <!-- A-Frame + AR.js -->
  <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/aframe-extras@6.1.1/dist/aframe-extras.animation-mixer.min.js"></script>
  <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>
  <link rel="stylesheet" href="https://veilands.lv/a-frame/point-and-click/style.css" />
  <script defer src="script.js"></script>
</head>
<body>

  <div class="toasts" aria-live="polite"></div>

  <div class="chips">
    <div id="chip1" class="chip" style="display:none;"><span class="dot"></span> KEY (1)</div>
    <div id="chip2" class="chip" style="display:none;"><span class="dot"></span> DOOR (2)</div>
    <div id="chip3" class="chip" style="display:none;"><span class="dot"></span> 🪣 BUCKET (3)</div>
    <div id="chip4" class="chip" style="display:none;"><span class="dot"></span> 💧 WELL (4)</div>
  </div>

  <div id="invLabel" class="inventory-label" style="display:none;">
    <span class="ic">📦</span>
    <span>Collected:</span>
    <strong id="invName">—</strong>
  </div>

  <div class="hud">
    <button id="btn-view" class="btn" aria-label="View"><span class="ic">👁</span><span>View</span></button>
    <button id="btn-use" class="btn" aria-label="Use"><span class="ic">🛠</span><span>Use</span></button>
    <button id="btn-collect" class="btn" aria-label="Collect"><span class="ic">🧺</span><span>Collect</span></button>
  </div>

  <a-scene embedded
    arjs="sourceType: webcam; debugUIEnabled: false;
          detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
          sourceWidth:1280; sourceHeight:960; displayWidth:1280; displayHeight:960;
          imageProcessing: { thresholdingBlockSize: 25, thresholdingConstant: -9 }"
    vr-mode-ui="enabled: false"
    renderer="logarithmicDepthBuffer: true"
    wire-up
  >

    <a-assets timeout="2000">
      <a-asset-item id="doorModel" src="door.glb"></a-asset-item>
    </a-assets>

    <!-- Marker: KEY (collect) -->
    <a-marker type="barcode" value="1">
      <a-entity
        item-meta="
          id: key;
          view: A small brass key.;
          use: It probably opens something.;
          collect: true;
                    needs_collected: ;
          use_success: ;
        "
      >
        <!-- Visual for the key -->
        <a-entity variant="key" class="collectable-model">
          <a-box depth="0.05" height="0.10" width="0.40" color="#ffd54f" position="0 0.05 0"></a-box>
          <a-sphere radius="0.05" color="#ffca28" position="-0.20 0.05 0"></a-sphere>
        </a-entity>
      </a-entity>
    </a-marker>

<!-- Marker: DOOR (needs key; opens on success; consumes key) -->
    <a-marker type="barcode" value="2">
      <!-- The main item-meta should be on the parent that holds the variants -->
      <a-entity
        item-meta="
          id: door;
          view: A sturdy door. It seems locked.;
          use: It's locked.;
          collect: false;
          needs_collected: key;
          use_success: The door opens.;
          change: door_open;             
          consume_collected: true;     
        "
        item-meta__door_open="    
          id: door_open;
          view: The door is now open.;
          use: It's already open.;
          collect: false;
        "
      >
        <!-- CLOSED variant (no animation) -->
        <a-entity variant="door">
          <a-entity gltf-model="#doorModel" scale="1 1 1"></a-entity>
        </a-entity>

        <!-- OPEN variant (plays once when variant becomes active) -->
        <a-entity variant="door_open">
          <a-entity
            gltf-model="#doorModel"
            animation-mixer="clip: anim; loop: once; clampWhenFinished: true"
            scale="1 1 1">
          </a-entity>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- Camera -->
    <a-entity camera></a-entity>
  </a-scene>
</body>
</html>
