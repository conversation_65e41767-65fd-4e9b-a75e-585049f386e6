<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Lectures Playground</title>
  <meta name="viewport" content="width=device-width,initial-scale=1" />

  <!-- CodeMirror -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/material-darker.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/xml/xml.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/javascript/javascript.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/css/css.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/htmlmixed/htmlmixed.min.js"></script>

  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="toolbar">
    <div class="title">Lectures Playground</div>
    <div class="controls">
      <button id="runBtn" type="button" title="Run (Ctrl+Enter)">Run</button>
      <button id="resetBtn" type="button" title="Reset current lecture">Reset</button>
      <label class="autorun">
        <input type="checkbox" id="autoRun" checked>
        Auto-run
      </label>
    </div>
  </header>

  <main class="layout">
    <!-- Left: Lectures -->
    <aside id="sidebar">
        <section class="lecture">
            <button class="lecture-header" type="button" aria-expanded="true">
            <span class="label">Lekcija 1</span>
            <span class="arrow">▼</span>
            </button>
            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="lecture_01"
                    data-src="lectures/lecture_01.html">
                AR.js markers
            </button>
            </div>
        </section>
        
        <section class="lecture">
            <button class="lecture-header" type="button" aria-expanded="true">
            <span class="label">Lekcija2</span>
            <span class="arrow">▼</span>
            </button>
            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="lecture_02"
                    data-src="lectures/lecture_02.html">
                animācija un formas
            </button>
            </div>

            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="lecture_03"
                    data-src="lectures/lecture_03.html">
                bonuss – interakcija
            </button>
            </div>
        </section>

                <section class="lecture">
            <button class="lecture-header" type="button" aria-expanded="true">
            <span class="label">Lekcija3</span>
            <span class="arrow">▼</span>
            </button>
            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="lecture_04"
                    data-src="lectures/lecture_04.html">
                point and click
            </button>
            </div>

            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="script"
                    data-src="lectures/script.js">
                script
            </button>
            </div>
            <div class="lecture-items">
            <button class="lecture-item active"
                    type="button"
                    data-key="style"
                    data-src="lectures/style.css">
                style
            </button>
            </div>
        </section>
    </aside>


    <!-- Middle: Code editor -->
    <section class="editor-wrap" aria-label="Code editor">
      <textarea id="editor" spellcheck="false"></textarea>
      <div class="hint" aria-hidden="true">Tip: press <kbd>Ctrl</kbd>+<kbd>Enter</kbd> to run</div>
    </section>

    <!-- Right: Live preview -->
    <section class="preview-wrap" aria-label="Live preview">
      <iframe id="preview" title="Live Preview"
              allow="camera *; microphone *; xr-spatial-tracking *; gyroscope *; accelerometer *;"
              allowfullscreen></iframe>
    </section>
  </main>

  <script src="app.js"></script>
</body>
</html>
