:root{
  --bg:#0b1020;
  --panel:#111731;
  --accent:#6ea8fe;
  --text:#e6e9f5;
  --muted:#9aa3c1;
  --border:#1c2342;
}

*{ box-sizing:border-box; }
html,body{ height:100%; }
body{
  margin:0;
  background:var(--bg);
  color:var(--text);
  font:14px/1.4 system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,"Helvetica Neue",Helvetica,Arial;
}

/* Top bar */
.toolbar{
  height:48px;
  display:flex;
  align-items:center;
  justify-content:space-between;
  padding:0 12px;
  border-bottom:1px solid var(--border);
  background:linear-gradient(180deg,#0f1530,#0c1230 60%,#0b1028);
}
.title{ font-weight:700; letter-spacing:.2px; }
.controls{ display:flex; gap:8px; align-items:center; }
button{
  background:var(--panel);
  border:1px solid var(--border);
  color:var(--text);
  padding:8px 12px;
  border-radius:8px;
  cursor:pointer;
}
button:hover{ border-color:#2a3566; }
.autorun{ display:flex; align-items:center; gap:6px; color:var(--muted); user-select:none; }

/* 3-column layout */
.layout{
  display:grid;
  grid-template-columns: 280px 1fr 1fr; /* <-- wider editor/preview */
  grid-template-rows: calc(100vh - 48px);
  height:calc(100vh - 48px);
}

/* Sidebar */
#sidebar{
  border-right:1px solid var(--border);
  background:linear-gradient(180deg,#0e1430,#0d1330 50%,#0c122c);
  padding:8px;
  overflow:auto;
}
.lecture{ border:1px solid var(--border); border-radius:12px; overflow:hidden; margin:8px 0; }
.lecture-header{
  width:100%;
  background:var(--panel);
  color:var(--text);
  padding:12px 14px;
  border:none;
  display:flex;
  justify-content:space-between;
  align-items:center;
  font-weight:600;
  cursor:pointer;
}
.lecture-header .arrow{ color:var(--muted); font-size:12px; transition:transform .2s ease; }
.lecture-header[aria-expanded="false"] .arrow{ transform:rotate(-90deg); }

.lecture-items{
  padding:8px; display:grid; gap:6px; border-top:1px solid var(--border); background:#0e1532;
}
.lecture-item{
  text-align:left;
  padding:10px 12px;
  background:#0f193e;
  border:1px solid var(--border);
  border-radius:10px;
}
.lecture-item.active, .lecture-item:hover{ border-color:#33407a; background:#111f4c; }

/* Editor */
.editor-wrap{
  position:relative;
  border-right:1px solid var(--border);
  background:#0b1020;
  min-width: 0; /* allows CodeMirror to expand properly */
}
.CodeMirror{
  height:100%;
  width:100%;
  font-size:13px;
  background:#0b1020;
  color:var(--text);
}
.cm-s-material-darker.CodeMirror{ background:#0b1020; }
.hint{
  position:absolute; right:8px; bottom:8px;
  color:var(--muted); font-size:12px; opacity:.8;
  pointer-events:none; /* never blocks clicks/paste */
}

/* Preview */
.preview-wrap{ background:#0a0f20; min-width: 0; }
#preview{
  width:100%; height:100%; border:0; background:#fff; border-left:1px solid var(--border);
}

/* Small screens */
@media (max-width: 1100px){
  .layout{ grid-template-columns: 240px 1fr; grid-template-rows: 50vh 50vh; }
  .preview-wrap{ grid-column: 1 / -1; }
}
