// ===== Generic Use/View/Collect engine (id-based) =====
const GRACE_MS = 1200; // keep markers virtually-visible briefly after loss

const Engine = {
  // marker state
  virtualVisible: new Set(),      // respects grace period
  trulyVisible: new Set(),        // raw found/lost
  lossTimers: {},                 // markerValue -> timeout id

  // inventory (single slot)
  collectedEl: null,              // the 3D container we moved (usually .collectable-model)
  collectedId: null,              // current id of collected item ("bucket", "water_bucket", ...)
  inventoryAnchor: null           // where collected item sits (top-of-screen)
};

// ------- UI helpers -------
function showToast(text, ms = 2600) {
  const wrap = document.querySelector('.toasts');
  if (!wrap) { console.warn('Toast container missing'); return; }
  const t = document.createElement('div');
  t.className = 'toast';
  t.textContent = text;
  wrap.appendChild(t);
  setTimeout(() => t.remove(), ms);
}
function setChip(id, on) {
  const el = document.getElementById(id);
  if (el) el.style.display = on ? 'flex' : 'none';
}
function updateInventoryLabel() {
  const box = document.getElementById('invLabel');
  const name = document.getElementById('invName');
  if (!box || !name) return;
  if (Engine.collectedId) {
    name.textContent = Engine.collectedId.replace(/_/g, ' '); // Make id human-readable
    box.style.display = 'flex';
  } else {
    name.textContent = '—';
    box.style.display = 'none';
  }
  // Trigger update for the 2D inventory bar (if present)
  window._updateInventoryBar && window._updateInventoryBar();
}
const anyVisible = () => Engine.virtualVisible.size > 0;

// ------- HUD binding (click + touch, stop propagation) -------
function bindHUD(sys) {
  const map = [
    ['btn-view',   () => sys.onView()],
    ['btn-use',    () => sys.onUse()],
    ['btn-collect',() => sys.onCollect()]
  ];
  for (const [id, fn] of map) {
    const el = document.getElementById(id);
    if (!el) { console.warn('[game] HUD button missing:', id); continue; }
    if (el.dataset.bound === '1') continue; // avoid double binding

    const handler = (e) => {
      e.preventDefault();
      e.stopPropagation();
      fn();
    };

    el.addEventListener('click', handler, {passive:false});
    el.addEventListener('touchstart', handler, {passive:false});

    el.dataset.bound = '1';
  }
}

// ------- A-Frame system -------
AFRAME.registerSystem('game', {
  init() {
    this.itemsByMarker = {}; // value -> { markerEl, itemEl, activeMetaName, metas }
    this.scene = this.el;

    // track last-known visibility to synthesize found/lost in tick()
    this._prevVis = {}; // value -> boolean

    // Mouse raycaster for clicking collected 3D item (if inventory anchor allows it)
    const cameraEl = document.querySelector('[camera]');
    if (cameraEl) {
      const cursor = document.createElement('a-entity');
      cursor.setAttribute('cursor', 'rayOrigin: mouse');
      cursor.setAttribute('raycaster', 'objects: .clickable'); // targets items with this class
      cameraEl.appendChild(cursor);
    }


    // Bind HUD immediately (script is defer'd, DOM is ready)
    bindHUD(this);
  },

  // Register a marker + item entity (itemEl has item-meta)
  registerItem(markerValue, markerEl, itemEl) {
    const metas = this.readMetas(itemEl);
    // Determine the initial active meta name. Default to 'base'.
    // If the base meta has a 'default_variant' property, use that as the starting point.
    let activeMetaName = 'base';
    if (metas.base && stringTrim(metas.base.default_variant)) {
        activeMetaName = stringTrim(metas.base.default_variant);
        if (!metas[activeMetaName]) {
            console.warn(`[game] Marker ${markerValue}: default_variant "${activeMetaName}" specified but no matching item-meta__${activeMetaName} found.`);
            activeMetaName = 'base'; // Fallback
        }
    }


    // Ensure current meta has an id
    if (!metas[activeMetaName] || !stringTrim(metas[activeMetaName].id)) {
      console.warn(`[game] item on marker ${markerValue} missing item-meta id for active variant "${activeMetaName}"; defaulting to "item${markerValue}"`);
      metas[activeMetaName] = metas[activeMetaName] || {};
      metas[activeMetaName].id = `item${markerValue}`;
    }

    this.itemsByMarker[markerValue] = { markerEl, itemEl, metas, activeMetaName };

    // Apply initial variant. Use the id from the active meta.
    this.applyVariantForItem(itemEl, metas[activeMetaName].id);

    // Listen to AR.js events
    // Use an event listener on the scene and filter by target, for better reliability
    const onSceneMarkerEvent = (evt) => {
      if (evt.target === markerEl) {
        if (evt.type === 'markerFound') {
          this._markFound(markerValue);
        } else if (evt.type === 'markerLost') {
          this._markLost(markerValue);
        }
      }
    };
    this.el.addEventListener('markerFound', onSceneMarkerEvent, true);
    this.el.addEventListener('markerLost', onSceneMarkerEvent, true);

    // Initialize prevVis so tick() can synthesize transitions for initial state
    // (object3D.visible is true for a brief moment even before markerFound)
    this._prevVis[markerValue] = !!(markerEl.object3D && markerEl.object3D.visible);
    if (this._prevVis[markerValue]) {
        // If already visible at init, trigger found immediately
        this._markFound(markerValue);
    }
  },

  // Synthesize found/lost by polling object3D.visible every frame
  tick() {
    for (const [valueStr, rec] of Object.entries(this.itemsByMarker)) {
      const v = +valueStr; // convert string key back to number
      const m = rec.markerEl;
      const now = !!(m && m.object3D && m.object3D.visible);
      const prev = !!this._prevVis[v];
      if (now && !prev) this._markFound(v);
      if (!now && prev) this._markLost(v);
      this._prevVis[v] = now;
    }
  },

  _markFound(v) {
    clearTimeout(Engine.lossTimers[v]);
    if (!Engine.trulyVisible.has(v)) Engine.trulyVisible.add(v);
    if (!Engine.virtualVisible.has(v)) {
      Engine.virtualVisible.add(v);
      this.updateChips(v, true);
      console.log(`Marker ${v} FOUND`);
    }
  },

  _markLost(v) {
    if (Engine.trulyVisible.has(v)) Engine.trulyVisible.delete(v);
    clearTimeout(Engine.lossTimers[v]);
    Engine.lossTimers[v] = setTimeout(() => {
      if (Engine.virtualVisible.has(v)) {
        Engine.virtualVisible.delete(v);
        this.updateChips(v, false);
        console.log(`Marker ${v} LOST (grace period expired)`);
      }
    }, GRACE_MS);
    console.log(`Marker ${v} LOST (starting grace period)`);
  },

  setInventoryAnchor(el) { Engine.inventoryAnchor = el; },

  updateChips(markerValue, isOn) {
    const chipId = `chip${markerValue}`;
    setChip(chipId, isOn);
  },

  // ---- VERB: VIEW ----
  onView() {
    let showed = false;
    for (const v of Engine.virtualVisible) {
      const s = this.itemsByMarker[v];
      if (!s) continue;
      const meta = s.metas[s.activeMetaName];
      if (meta.view) { showToast(meta.view); showed = true; }
    }
    if (!showed) showToast('Point at an item and press View.');
  },

  // ---- VERB: USE ----
  onUse() {
    // If we have something collected, try to use it on visible target(s)
    if (Engine.collectedEl && Engine.collectedId) {
      let usedOnTarget = false;
      for (const v of Engine.virtualVisible) {
        const s = this.itemsByMarker[v];
        if (!s) continue;
        const meta = s.metas[s.activeMetaName];

        // Match by required id (supports comma-separated list)
        if (matchesNeeded(meta.needs_collected, Engine.collectedId)) {
          usedOnTarget = true;

          // Success!
          const msg = meta.use_success || 'Success.';
          showToast(msg);

          // Target meta change (e.g., flower -> grown_flower)
          const changeTo = stringTrim(meta.change);
          if (changeTo && s.metas[changeTo]) {
            s.activeMetaName = changeTo; // Update the item's state
            const newMeta = s.metas[changeTo];
            if (stringTrim(newMeta.id)) {
                this.applyVariantForItem(s.itemEl, newMeta.id); // Apply new visual variant
            } else {
                 console.warn(`[game] New meta "${changeTo}" on marker ${v} is missing an 'id' attribute. Variant might not switch correctly.`);
            }
          } else if (changeTo) {
             console.warn(`[game] Change target "${changeTo}" for marker ${v} not found.`);
          }

          // Transform the collected item (e.g., bucket -> water_bucket)
          const transformTo = stringTrim(meta.transform_collected_to);
          if (transformTo) {
            Engine.collectedId = transformTo;
            this.applyVariantForCollected(Engine.collectedEl, transformTo);
            updateInventoryLabel();
          }

          // Optional consumption (e.g., giving flower to Mom)
          if (truthy(meta.consume_collected)) {
            Engine.collectedEl.setAttribute('visible', false);
            if (Engine.collectedEl.object3D) Engine.collectedEl.object3D.visible = false;
            // Remove click handler from consumed item in inventory
            if (Engine.collectedEl.onclick) Engine.collectedEl.onclick = null;
            Engine.collectedEl.classList.remove('clickable');

            Engine.collectedEl = null;
            Engine.collectedId = null;
            updateInventoryLabel();
          }
          // Only use on ONE target at a time, so return after first success
          return;
        }
      }

      if (!usedOnTarget) {
         // We had an item but no valid target in view
         showToast('On what?');
      }
      return;
    }

    // No item collected: using on visible items shows their "use" prompt
    if (anyVisible()) {
      let showed = false;
      for (const v of Engine.virtualVisible) {
        const s = this.itemsByMarker[v];
        if (!s) continue;
        const meta = s.metas[s.activeMetaName];
        if (meta.use) { showToast(meta.use); showed = true; }
      }
      if (!showed) showToast('Nothing to use right now.');
    } else {
      showToast('Nothing to use right now.');
    }
  },

  // ---- VERB: COLLECT ----
  onCollect() {
    // If an item is already collected, we can't collect another.
    if (Engine.collectedId) {
        showToast('You already have an item collected. Use it first!');
        return;
    }

    // Try to collect any visible item with collect=true
    for (const v of Engine.virtualVisible) {
      const s = this.itemsByMarker[v];
      if (!s) continue;
      const meta = s.metas[s.activeMetaName];

      const collectVal = stringTrim(meta.collect);
      if (collectVal === 'true') {
        // Remove item from scene before collecting
        s.itemEl.setAttribute('visible', false);
        if (s.itemEl.object3D) s.itemEl.object3D.visible = false;
        return this.collectItem(s.itemEl, meta.id);
      }
      if (collectVal && collectVal !== 'false') {
        return showToast(collectVal); // reason why not
      }
    }
    showToast('No item in view to collect.');
  },

  collectItem(itemEl, itemId) {
    // Move the designated collectable container (must exist for collectable items)
    let model = itemEl.querySelector('.collectable-model');
    if (!model) { showToast('This item has no collectable-model.'); return; }

    const anchor = Engine.inventoryAnchor || document.querySelector('[camera]');
    if (!anchor) { console.error('Inventory anchor missing!'); return; }

    // Re-parent to inventory anchor
    // Detach from current parent first to avoid A-Frame warnings/issues
    if (model.parentNode) {
        model.parentNode.removeChild(model);
    }
    anchor.appendChild(model);

    // Place it top-center, slightly forward in camera space
    model.setAttribute('position', '0 0.35 -0.7'); // Fixed position relative to camera
    model.setAttribute('rotation', '0 0 0');
    model.setAttribute('scale', '0.65 0.65 0.65');
    model.setAttribute('visible', true);
    if (model.object3D) model.object3D.visible = true;
    model.classList.add('clickable'); // Make it clickable in inventory

    // Clicking the collected model attempts a use
    model.onclick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.onUse();
    };

    // Record current collected id and ensure correct variant is shown
    Engine.collectedEl = model;
    Engine.collectedId = itemId || '';
    if (Engine.collectedId) this.applyVariantForCollected(model, Engine.collectedId);

    updateInventoryLabel();
    showToast('Collected.');
  },

  // ---- Meta reading / switching ----
  readMetas(itemEl) {
    // Base meta from component (parsed by A-Frame)
    const base = itemEl.getAttribute('item-meta') || {};

    // Multi-instance syntax preferred: item-meta__name="..."
    const metas = { base };
    // Also accept item-meta__* attributes
    for (const attr of itemEl.attributes) {
      if (attr.name.startsWith('item-meta__')) {
        const name = attr.name.split('__')[1];
        // Ensure that if an attribute is provided, we parse it
        // A-Frame might already parse it if defined in schema, but this is a fallback.
        const obj = itemEl.getAttribute(attr.name) || parseKV(attr.value);
        metas[name] = obj;
      }
      // Fallback: item-meta2="..." style (for older patterns, less preferred)
      if (/^item-meta\d+$/.test(attr.name)) {
        metas[attr.name.replace('item-meta','meta')] = parseKV(attr.value);
      }
    }
    return metas;
  },

  // Toggle which variant is visible for an ITEM (in-scene)
  applyVariantForItem(itemEl, id) {
    // Select all direct children with a 'variant' attribute
    // Note: If variant entities are nested deeper, adjust querySelectorAll
    const nodes = itemEl.querySelectorAll(':scope > [variant]');
    if (!nodes.length) {
        // If no direct variant children, check for a 'collectable-model' child for its variants
        const collectableModel = itemEl.querySelector('.collectable-model');
        if (collectableModel) {
            this.applyVariantForCollected(collectableModel, id);
        }
        return;
    }

    nodes.forEach(n => {
      const isTargetVariant = n.getAttribute('variant') === id;
      n.setAttribute('visible', isTargetVariant);

      // If this is the variant we just made visible, restart any animation-mixer on it or its children
      if (isTargetVariant) {
        // Find animation-mixer on itself OR its children (like the GLB model)
        const animatedEl = n.getAttribute('animation-mixer') ? n : n.querySelector('[animation-mixer]');

        if (animatedEl) {
          const cfg = animatedEl.getAttribute('animation-mixer');
          animatedEl.removeAttribute('animation-mixer');
          // Use Promise.resolve().then() to ensure component re-initializes cleanly
          Promise.resolve().then(() => animatedEl.setAttribute('animation-mixer', cfg));
        }
      }
    });
  },

  // Toggle which variant is visible for the COLLECTED model (in inventory)
  applyVariantForCollected(modelEl, id) {
    // Select all direct children with a 'variant' attribute inside the collectable-model
    const nodes = modelEl.querySelectorAll(':scope > [variant]');
    if (!nodes.length) return;

    nodes.forEach(n => {
      const isTargetVariant = n.getAttribute('variant') === id;
      n.setAttribute('visible', isTargetVariant);

      // If this is the variant we just made visible, restart any animation-mixer on it or its children
      if (isTargetVariant) {
        // Find animation-mixer on itself OR its children (like the GLB model)
        const animatedEl = n.getAttribute('animation-mixer') ? n : n.querySelector('[animation-mixer]');

        if (animatedEl) {
          const cfg = animatedEl.getAttribute('animation-mixer');
          animatedEl.removeAttribute('animation-mixer');
          // Use Promise.resolve().then() to ensure component re-initializes cleanly
          Promise.resolve().then(() => animatedEl.setAttribute('animation-mixer', cfg));
        }
      }
    });
  }
});

// ------- Component to carry item behavior -------
// multiple:true lets you declare item-meta__meta2="..."
AFRAME.registerComponent('item-meta', {
  multiple: true,
  schema: {
    id: {type:'string', default:''},                 // logical item id (bucket, water_bucket, flower, grown_flower, mom, well)
    view: {type:'string', default:''},
    use: {type:'string', default:''},
    collect: {type:'string', default:''},            // "true" / "false" / reason string
    needs_collected: {type:'string', default:''},    // required collected id(s), comma-separated
    use_success: {type:'string', default:''},        // shown on success
    change: {type:'string', default:''},             // switch this item's meta to another instance name (e.g., meta2)
    transform_collected_to: {type:'string', default:''}, // change the collected id (e.g., bucket -> water_bucket)
    consume_collected: {type:'string', default:''},  // "true" to remove collected on success (default false)
    default_variant: {type:'string', default:''}     // Optional: specify which item-meta__variant name is active at start (e.g., 'open' for a door)
  }
});

// ------- Wire up once the scene is ready -------
AFRAME.registerComponent('wire-up', {
  init() {
    const sys = this.el.sceneEl.systems['game'];
    const doRegister = () => {
      // Dynamically find all markers and register them
      const markers = document.querySelectorAll('a-marker[type="barcode"][value]');
      markers.forEach(marker => {
        const markerValue = parseInt(marker.getAttribute('value'), 10);
        const item = marker.querySelector('[item-meta]');
        if (item) {
          sys.registerItem(markerValue, marker, item);
        } else {
          console.warn(`[wire-up] Marker ${markerValue} found, but no child <a-entity item-meta> detected.`);
        }
      });

      // Inventory anchor (top of screen, child of camera)
      const cam = document.querySelector('[camera]');
      if (!cam) { console.error('Camera entity not found!'); return; }
      const inv = document.createElement('a-entity');
      inv.setAttribute('position', '0 0.35 -0.7'); // higher/farther so it's always visible
      cam.appendChild(inv);
      sys.setInventoryAnchor(inv);

      // (Re)bind HUD — idempotent
      bindHUD(sys);

      // Initial update for inventory label and bar
      updateInventoryLabel();
    };

    if (this.el.hasLoaded) doRegister();
    else this.el.addEventListener('loaded', doRegister);
  }
});

// ------- Small helpers -------
function stringTrim(v){ return (v || '').toString().trim(); }
function truthy(v){ return stringTrim(v) === 'true'; }
function parseCSV(s){
  return stringTrim(s).split(',').map(x=>x.trim()).filter(Boolean);
}
function matchesNeeded(needs, haveId){
  if (!haveId) return false;
  const list = parseCSV(needs);
  if (!list.length) return false;
  return list.includes(haveId);
}

// Fallback manual parser for "a: 1; b: two;" style strings
function parseKV(s){
  const out = {};
  if (!s) return out;
  s.split(';').forEach(pair=>{
    const i = pair.indexOf(':');
    if (i>-1){
      const k = pair.slice(0,i).trim();
      const v = pair.slice(i+1).trim();
      if (k) out[k] = v;
    }
  });
  return out;
}

/* Show the icon whose data-item matches the current collected id.
   Works without changing your engine (reads #invName / Engine.collectedId). */
(function () {
  const bar = document.getElementById('inventoryBar');
  if (!bar) return;

  // Collect icon entries. Support either data-item="id" or data-items="id1,id2"
  const icons = Array.from(bar.querySelectorAll('[data-item],[data-items]')).map(el => {
    const single = el.getAttribute('data-item');
    const multi  = el.getAttribute('data-items');
    const ids = (multi || single || '')
      .split(',')
      .map(s => s.trim())
      .filter(Boolean);
    return { el, ids };
  });

  // Normalize a human label -> engine-ish id (e.g., "Water Bucket" -> "water_bucket")
  function toIdish(s) {
    return (s || '')
      .toLowerCase()
      .replace(/[^\w]+/g, '_')
      .replace(/^_+|_+$/g, '');
  }

  function currentId() {
    // 1) Engine signal (preferred)
    if (window.Engine && Engine.collectedId) return String(Engine.collectedId);

    // 2) Fallback: read the visible label the engine already updates
    const nameNode = document.getElementById('invName');
    const text = nameNode ? nameNode.textContent.trim() : '';
    // Treat empty, dash, em dash as "no item"
    if (!text || text === '-' || text === '—') return '';
    return toIdish(text);
  }

  function render() {
    const id = currentId();

    // Hide everything if no item is collected
    if (!id) {
      icons.forEach(({el}) => { el.style.display = 'none'; });
      return;
    }

    // Show icons whose id list contains the current id; hide the rest
    let anyShown = false;
    icons.forEach(({el, ids}) => {
      const match = ids.includes(id);
      el.style.display = match ? 'inline-flex' : 'none';
      if (match) anyShown = true;
    });

    // If nothing matched (e.g., you forgot to add an icon for this id), hide all
    if (!anyShown) {
      icons.forEach(({el}) => { el.style.display = 'none'; });
    }
  }

  // Assign a global function for other parts of the script to trigger updates
  window._updateInventoryBar = render;

  // React when the engine updates the label (collect/use/transform/consume)
  const nameNode = document.getElementById('invName');
  if (nameNode && 'MutationObserver' in window) {
    const obs = new MutationObserver(render);
    obs.observe(nameNode, { childList: true, characterData: true, subtree: true });
  }

  // Safety nets:
  // - Periodic check (covers programmatic changes to Engine.collectedId)
  // - Hide icons when the label container is hidden (consumed)
  setInterval(render, 200);
  const label = document.getElementById('invLabel');
  if (label && 'MutationObserver' in window) {
    const obs2 = new MutationObserver(render);
    obs2.observe(label, { attributes: true, attributeFilter: ['style', 'class'] });
  }
    // Initial paint
  render();
})();