/* pointclick-engine.js — built-ins demo ready */
(function () {
  'use strict';

  const parsePos = (str) => {
    if (!str) return { x: 0, y: 0, z: 0 };
    const parts = str.split(/[,\s]+/).filter(Boolean).map(parseFloat);
    if (parts.length === 2) return { x: parts[0], y: 0, z: parts[1] };
    return { x: parts[0] || 0, y: parts[1] || 0, z: parts[2] || 0 };
  };

  const INVENTORY_SIZE = 4;

  const Drag = {
    active: null, ghost: null,
    start(imgEl, payload) {
      this.active = { imgEl, payload };
      this.ghost = imgEl.cloneNode(true);
      this.ghost.classList.add('dragging');
      document.body.appendChild(this.ghost);
      document.body.style.cursor = 'grabbing';
    },
    move(x, y) {
      if (this.ghost) { this.ghost.style.left = x + 'px'; this.ghost.style.top = y + 'px'; }
    },
    end() {
      if (this.ghost) this.ghost.remove();
      this.ghost = null; this.active = null; document.body.style.cursor = '';
    }
  };

  function raycastScene(sceneEl, cameraEl, clientX, clientY) {
    const THREE = AFRAME.THREE;
    const rect = sceneEl.canvas.getBoundingClientRect();
    const x = ((clientX - rect.left) / rect.width) * 2 - 1;
    const y = -((clientY - rect.top) / rect.height) * 2 + 1;
    const camera = cameraEl.getObject3D('camera'); if (!camera) return null;
    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(new THREE.Vector2(x, y), camera);
    const objs = [];
    sceneEl.object3D.traverse(obj => {
      if (obj.el && obj.el.classList && obj.el.classList.contains('pc-interactive')) objs.push(obj);
    });
    const hits = raycaster.intersectObjects(objs, true);
    return hits[0]?.object?.el || null;
  }

  class Inventory {
    constructor(hudEl) {
      this.hudEl = hudEl;
      this.slots = new Array(INVENTORY_SIZE).fill(null);
      this.wireDrag();
    }
    firstEmptySlot() { return this.slots.findIndex(v => v === null); }
    add(iconSrc, keyName) {
      const idx = this.firstEmptySlot();
      if (idx === -1) return false;
      const slotEl = this.hudEl.querySelector(`.slot[data-slot="${idx}"]`);
      const img = document.createElement('img');
      img.className = 'icon'; img.src = iconSrc || ''; img.draggable = false; img.dataset.key = keyName || '';
      slotEl.appendChild(img);
      this.slots[idx] = { iconSrc, keyName, img };
      return true;
    }
    removeByImg(img) {
      const idx = this.slots.findIndex(s => s?.img === img);
      if (idx !== -1) { this.slots[idx]?.img?.remove(); this.slots[idx] = null; }
    }
    wireDrag() {
      const onDown = (e) => {
        const t = e.target.closest('.icon'); if (!t) return; e.preventDefault();
        Drag.start(t, { keyName: t.dataset.key, img: t });
      };
      const onMove = (e) => { if (!Drag.active) return; const p = e.touches ? e.touches[0] : e; Drag.move(p.clientX, p.clientY); };
      const onUp = (e) => {
        if (!Drag.active) return; const p = e.changedTouches ? e.changedTouches[0] : e;
        this.hudEl.dispatchEvent(new CustomEvent('inventory-drop', { detail: { clientX: p.clientX, clientY: p.clientY, payload: Drag.active.payload } }));
        Drag.end();
      };
      document.addEventListener('pointerdown', onDown);
      document.addEventListener('pointermove', onMove);
      document.addEventListener('pointerup', onUp);
      document.addEventListener('touchstart', onDown, { passive: false });
      document.addEventListener('touchmove', onMove, { passive: false });
      document.addEventListener('touchend', onUp);
    }
  }

  AFRAME.registerComponent('pointclick-engine', {
    schema: {
      mapSrc: { type: 'string' },
      startRoom: { type: 'string', default: '' },
      blockWidth: { type: 'number', default: 1.6 },
      blockDepth: { type: 'number', default: 0.9 },
      moveMs: { type: 'int', default: 2000 },
      view: { type: 'string', default: 'panel' },     // 'panel' or 'room'
      viewAxis: { type: 'string', default: 'z' }      // 'z' = face +Z (default), 'x' = face +X
    },

    init() {
      this.sceneEl = this.el.sceneEl;
      this.assetsEl = document.getElementById('assets');
      this.rig = document.getElementById('rig');
      this.camera = document.getElementById('camera');
      this.inventory = new Inventory(document.getElementById('inventory'));
      this.grid = [];
      this.letterToCoord = new Map();
      this.currentLetter = null;
      this.currentRoomEl = null;
      this.targetRoomEl = null;
      this.itemProtos = [];
      this.doorProtos = [];
      this.isMoving = false;

      this.collectPrototypes();
      this.inventory.hudEl.addEventListener('inventory-drop', (e) => this.onInventoryDrop(e.detail));

      this.loadMap(this.data.mapSrc)
        .then(() => this.gotoStart())
        .catch(err => console.error('[Engine] map load failed:', err));

      window.addEventListener('resize', () => this.fitCameraToRoom());
      this.ensureLandscapeNotice();
    },

    collectPrototypes() {
      const assets = document.getElementById('assets');
      // Accept either real assets (with src) or plain data prototypes
      const items = [
        ...assets.querySelectorAll('a-asset-item[pickable]'),
        ...assets.querySelectorAll('[data-proto="item"]')
      ];
      const doors = [
        ...assets.querySelectorAll('a-asset-item[door]'),
        ...assets.querySelectorAll('[data-proto="door"]')
      ];
      this.itemProtos = items;
      this.doorProtos = doors;
    },

    async loadMap(src) {
      const res = await fetch(src);
      const text = await res.text();
      const rows = text
        .split('\n').map(l => l.trim()).filter(Boolean)
        .map(l => l.replace(/[，]/g, ','))
        .map(l => l.split(',').map(t => t.trim().toLowerCase()).filter(Boolean));
      const maxCols = Math.max(...rows.map(r => r.length));
      rows.forEach(r => { while (r.length < maxCols) r.push('x'); });
      this.grid = rows;
      this.letterToCoord.clear();
      for (let r = 0; r < rows.length; r++) {
        for (let c = 0; c < rows[r].length; c++) {
          const cell = rows[r][c];
          if (cell !== 'x' && !this.letterToCoord.has(cell)) this.letterToCoord.set(cell, { row: r, col: c });
        }
      }
    },

    gotoStart() {
      let letter = this.data.startRoom || null;
      if (!letter || !this.letterToCoord.has(letter)) {
        for (const [k] of this.letterToCoord) { letter = k; break; }
      }
      if (!letter) { console.error('[Engine] no start room'); return; }
      this.currentLetter = letter;
      this.instantiateRoom(letter);
      this.placeRigAt(letter);
      this.fitCameraToRoom();
    },

    roomWorldPos(letter) {
      const coord = this.letterToCoord.get(letter);
      const BW = this.data.blockWidth, BD = this.data.blockDepth;
      const axis = (this.data.viewAxis || 'z').toLowerCase();

      if (axis === 'z') {
        // columns → +X, rows → +Z (camera in +Z looking −Z)  ← side-scroller feel
        return { x: coord.col * BW, y: 0, z: coord.row * BD };
      } else {
        // columns → +Z, rows → +X (camera in +X looking −X)
        return { x: coord.row * BD, y: 0, z: coord.col * BW };
      }
    },

    placeRigAt(letter) {
      const p = this.roomWorldPos(letter);
      this.rig.setAttribute('position', `${p.x} 0 ${p.z}`);
      this.rig.setAttribute('rotation', `0 0 0`);     // ensure no yaw on the rig
      this.camera.setAttribute('rotation', `0 0 0`);  // we control aim in fitCameraToRoom()
    },

    fitCameraToRoom() {
      const camObj = this.camera.getObject3D('camera');
      if (!camObj || !this.currentLetter) return;

      const BW = this.data.blockWidth;   // 1.6 (panel width)
      const BH = this.data.blockDepth;   // 0.9 (panel height)
      const yCenter = BH / 2;            // 0.45

      // compute distance so the width fits horizontally
      const vFOV = (camObj.fov || 80) * Math.PI / 180;
      const aspect = camObj.aspect || (this.sceneEl.canvas?.width / this.sceneEl.canvas?.height) || (16/9);
      const hFOV = 2 * Math.atan(Math.tan(vFOV / 2) * aspect);
      const dist = (BW / 2) / Math.tan(hFOV / 2);

      const axis = (this.data.viewAxis || 'z').toLowerCase();


        if (this.data.view === 'panel') {
          if (axis === 'z' && Math.abs(pos.z) < 1e-4) pos.z = -0.02; // sit slightly in front of the wall
          if (axis === 'x' && Math.abs(pos.x) < 1e-4) pos.x = -0.02; // same idea but for +X-facing panels
        }


      camObj.updateProjectionMatrix();
    },

    instantiateRoom(letter) {
      if (this.currentRoomEl) { this.currentRoomEl.remove(); this.currentRoomEl = null; }

      const assetId = `#room-${letter}`;
      const asset = this.assetsEl.querySelector(assetId);
      const roomPos = this.roomWorldPos(letter);

      // Room container
      const roomEl = document.createElement('a-entity');
      roomEl.className = 'pc-room';
      roomEl.setAttribute('position', `${roomPos.x} 0 ${roomPos.z}`);

      if (asset && asset.getAttribute('src')) {
        const modelEl = document.createElement('a-entity');
        modelEl.setAttribute('gltf-model', assetId);
        roomEl.appendChild(modelEl);
      } else {
        // === Built-in procedural room ===
        this.buildProceduralRoom(roomEl, letter);
      }

      // Doors that live in this room
      this.doorProtos.filter(p => (p.getAttribute('room') || '').toLowerCase() === letter)
        .forEach(proto => {
          const pos = parsePos(proto.getAttribute('pos'));
          const doorEl = document.createElement('a-entity');
          doorEl.classList.add('pc-interactive', 'pc-door');
          const hasSrc = !!proto.getAttribute('src');
          if (hasSrc) {
            doorEl.setAttribute('gltf-model', `#${proto.id}`);
          } else {
            doorEl.setAttribute('geometry', 'primitive: box; width: 0.28; height: 0.6; depth: 0.06');
            doorEl.setAttribute('material', 'color: #8a8a8a; metalness: 0.2; roughness: 0.6');
          }
          doorEl.setAttribute('position', `${pos.x} ${pos.y} ${pos.z}`);

          const dest = (proto.getAttribute('door') || '').replace(/^#?/, '');
          const connect = proto.getAttribute('connect') || '';
          if (proto.hasAttribute('animation__resolved')) {
            doorEl.setAttribute('animation__resolved', proto.getAttribute('animation__resolved'));
          }
          doorEl.dataset.unlocked = connect ? 'false' : 'true';
          doorEl.dataset.connect = connect;
          doorEl.dataset.dest = dest;

          doorEl.addEventListener('click', () => {
            if (this.isMoving) return;
            if (doorEl.dataset.unlocked !== 'true') return;
            const letterDest = dest.replace(/^room-/, '');
            this.moveTo(letterDest);
          });

          roomEl.appendChild(doorEl);
        });

      // Items that live in this room
      this.itemProtos.filter(p => (p.getAttribute('room') || '').toLowerCase() === letter)
        .forEach(proto => {
          const pos = parsePos(proto.getAttribute('pos'));
          const itemEl = document.createElement('a-entity');
          itemEl.classList.add('pc-interactive', 'pc-item');
          const hasSrc = !!proto.getAttribute('src');
          if (hasSrc) {
            itemEl.setAttribute('gltf-model', `#${proto.id}`);
          } else {
            itemEl.setAttribute('geometry', 'primitive: sphere; radius: 0.05');
            itemEl.setAttribute('material', 'color: #ffd24d; metalness: 0.3; roughness: 0.2; emissive: #332200; emissiveIntensity: 0.4');
          }
          itemEl.setAttribute('position', `${pos.x} ${pos.y} ${pos.z}`);

          if (proto.hasAttribute('animation__hover')) {
            itemEl.setAttribute('animation__hover', proto.getAttribute('animation__hover'));
          }

          const icon = proto.getAttribute('icon') || '';
          const keyName = proto.getAttribute('connect') || '';

          itemEl.addEventListener('mouseenter', () => {
            const comp = itemEl.getAttribute('animation__hover'); if (comp) itemEl.setAttribute('animation__hover', comp);
          });
          itemEl.addEventListener('click', () => {
            const ok = this.inventory.add(icon, keyName);
            if (ok) itemEl.remove();
          });

          roomEl.appendChild(itemEl);
        });

      this.sceneEl.appendChild(roomEl);
      this.currentRoomEl = roomEl;
    },

    buildProceduralRoom(roomEl, letter) {
      const BW = this.data.blockWidth;   // width  1.6
      const BH = this.data.blockDepth;   // height 0.9
      const yCenter = BH / 2;
      const axis = (this.data.viewAxis || 'z').toLowerCase();

      if (this.data.view === 'panel') {
        const wall = document.createElement('a-entity');
        wall.setAttribute('geometry', `primitive: plane; width: ${BW}; height: ${BH}`);
        wall.setAttribute('material', 'color: #1f1f1f; roughness: 0.9; side: double');
        wall.setAttribute('position', `0 ${yCenter} 0`);
        // face +Z or +X depending on axis
        wall.setAttribute('rotation', axis === 'z' ? '0 0 0' : '0 90 0');
        roomEl.appendChild(wall);

        const label = document.createElement('a-entity');
        label.setAttribute('text', `value: ${letter.toUpperCase()}; align: left; width: 1`);
        // a tiny nudge off the wall so it doesn’t z-fight
        if (axis === 'z') label.setAttribute('position', `${-BW/2 + 0.05} ${BH - 0.05} 0.001`);
        else              label.setAttribute('position', `0.001 ${BH - 0.05} ${-BW/2 + 0.05}`);
        roomEl.appendChild(label);
        return;
      }

      // (optional) your original 4-wall room can stay here if you want
    },

    ensureRoomInstantiated(letter) {
      if (this.targetRoomEl) { this.targetRoomEl.remove(); this.targetRoomEl = null; }
      const roomPos = this.roomWorldPos(letter);
      const roomEl = document.createElement('a-entity');
      roomEl.className = 'pc-room target';
      roomEl.setAttribute('position', `${roomPos.x} 0 ${roomPos.z}`);
      // Just a minimalist shell so you perceive motion
      this.buildProceduralRoom(roomEl, letter);
      this.sceneEl.appendChild(roomEl);
      this.targetRoomEl = roomEl;
    },

    moveTo(letterDest) {
      if (!this.letterToCoord.has(letterDest)) return;
      this.isMoving = true;
      this.ensureRoomInstantiated(letterDest);

      const from = this.roomWorldPos(this.currentLetter);
      const to = this.roomWorldPos(letterDest);

      this.rig.removeAttribute('animation__move');
      this.rig.setAttribute('animation__move', {
        property: 'position',
        from: `${from.x} 0 ${from.z}`,
        to: `${to.x} 0 ${to.z}`,
        dur: this.data.moveMs,
        easing: 'linear'
      });

      const onComplete = () => {
        this.rig.removeEventListener('animationcomplete__move', onComplete);
        this.currentLetter = letterDest;
        this.instantiateRoom(letterDest);
        this.fitCameraToRoom();
        if (this.targetRoomEl) { this.targetRoomEl.remove(); this.targetRoomEl = null; }
        this.isMoving = false;
      };
      this.rig.addEventListener('animationcomplete__move', onComplete);
    },

    onInventoryDrop({ clientX, clientY, payload }) {
      const hitEl = raycastScene(this.sceneEl, this.camera, clientX, clientY);
      if (!hitEl || !hitEl.classList.contains('pc-door')) return;
      const need = hitEl.dataset.connect || '';
      if (!need) return;
      if (payload.keyName === need) {
        hitEl.dataset.unlocked = 'true';
        this.inventory.removeByImg(payload.img);
        const comp = hitEl.getAttribute('animation__resolved');
        if (comp) hitEl.setAttribute('animation__resolved', comp);
      }
    },

    ensureLandscapeNotice() {
      const notice = document.getElementById('rotateNotice');
      const update = () => {
        const isPortrait = window.matchMedia('(orientation: portrait)').matches;
        notice.style.display = isPortrait ? 'flex' : 'none';
      };
      update();
      window.addEventListener('resize', update);
    }
  });
})();
