<!DOCTYPE html>
<html>
<head>
  <!-- https://veilands.lv/a-frame/lectures/lecture_03.html -->
  <meta charset="UTF-8" />
  <title>AR.js Marker Combos</title>
  <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
  <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
  <script src="lecture_03.js"></script>

  <style>
    #hud{position:fixed;left:10px;top:10px;z-index:10;padding:6px 10px;background:rgba(0,0,0,.6);
         color:#fff;font:14px system-ui;border-radius:8px}
  </style>

  
</head>
<body>
  <div id="hud">No markers</div>

  <a-scene
    embedded
    combo-controller
    arjs="sourceType: webcam; debugUIEnabled: false;
          detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
          imageProcessing: { thresholdingBlockSize: 25, thresholdingConstant: -9 }"
    vr-mode-ui="enabled: false"
    renderer="logarithmicDepthBuffer: true">

    <!-- Marker 24 -->
    <a-marker type="barcode" value="24" emitevents="true">
      <a-cylinder id="cyl"
        position="0 0.5 0" radius="0.3" height="1" material="color: blue"
        animation__solo="property: scale; to: 1 2 1; dur: 700; dir: alternate; easing: easeInOutQuad;
                         startEvents: combo-24; restartEvents: combo-24; pauseEvents: combo-clear; autoplay: false"
        animation__2425="property: material.color; to: #7C3AED; dur: 300;
                         startEvents: combo-24+25; restartEvents: combo-24+25; pauseEvents: combo-clear; autoplay: false"
        animation__2426="property: position; to: 0 1.3 0; dur: 500; dir: alternate; easing: easeOutElastic;
                         startEvents: combo-24+26; restartEvents: combo-24+26; pauseEvents: combo-clear; autoplay: false"
        animation__all="property: rotation; to: 0 720 0; dur: 500; easing: easeOutExpo;
                        startEvents: combo-24+25+26; restartEvents: combo-24+25+26; pauseEvents: combo-clear; autoplay: false">
      </a-cylinder>
    </a-marker>

    <!-- Marker 25 -->
    <a-marker type="barcode" value="25" emitevents="true">
      <a-box id="box"
        position="0 0.5 0" scale="0.5 0.5 0.5" material="color: green"
        animation__solo="property: rotation; to: 0 360 0; dur: 900; easing: linear;
                         startEvents: combo-25; restartEvents: combo-25; pauseEvents: combo-clear; autoplay: false"
        animation__2425s="property: scale; to: 0.8 0.8 0.8; dur: 280; dir: alternate; easing: easeOutCubic;
                          startEvents: combo-24+25; restartEvents: combo-24+25; pauseEvents: combo-clear; autoplay: false"
        animation__2526="property: scale; to: 0.9 0.9 0.9; dur: 240; dir: alternate; easing: easeInOutSine;
                         startEvents: combo-25+26; restartEvents: combo-25+26; pauseEvents: combo-clear; autoplay: false"
        animation__all="property: rotation; to: 0 540 0; dur: 600; easing: easeOutQuad;
                        startEvents: combo-24+25+26; restartEvents: combo-24+25+26; pauseEvents: combo-clear; autoplay: false">
      </a-box>
    </a-marker>

    <!-- Marker 26 -->
    <a-marker type="barcode" value="26" emitevents="true">
      <a-torus id="torus"
        position="0 0.5 0" color="orange" radius="1" scale="0.5 0.5 0.5"
        animation__solo1="property: material.color; to: tomato; dur: 350; dir: alternate; easing: easeOutQuad;
                          startEvents: combo-26; restartEvents: combo-26; pauseEvents: combo-clear; autoplay: false"
        animation__solo2="property: position; to: 0 1.2 0; dur: 450; dir: alternate; easing: easeOutSine;
                          startEvents: combo-26; restartEvents: combo-26; pauseEvents: combo-clear; autoplay: false"
        animation__2426="property: rotation; to: 0 180 0; dur: 800; dir: alternate; easing: easeInSine;
                         startEvents: combo-24+26; restartEvents: combo-24+26; pauseEvents: combo-clear; autoplay: false"
        animation__2526="property: material.color; to: #00D1FF; dur: 300; easing: linear;
                         startEvents: combo-25+26; restartEvents: combo-25+26; pauseEvents: combo-clear; autoplay: false"
        animation__all="property: position; to: 0 1.4 0; dur: 450; dir: alternate; easing: easeOutBack;
                        startEvents: combo-24+25+26; restartEvents: combo-24+25+26; pauseEvents: combo-clear; autoplay: false">
      </a-torus>
    </a-marker>

    <a-entity camera></a-entity>
  </a-scene>
</body>
</html>