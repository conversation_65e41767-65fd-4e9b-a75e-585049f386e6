<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>AR Use / View / Collect — ID-based</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover">

  <!-- A-Frame + AR.js (AR-js-org build recommended) -->
  <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>

  <link rel="stylesheet" href="style.css" />
  <!-- defer so DOM exists before script runs -->
  <script defer src="script.js"></script>
</head>
<body>
  <!-- Screen text (toasts) -->
  <div class="toasts" aria-live="polite"></div>

  <!-- Visible marker chips -->
  <div class="chips">
    <div id="chip1" class="chip" style="display:none;"><span class="dot"></span> 👩 MOM (1)</div>
    <div id="chip2" class="chip" style="display:none;"><span class="dot"></span> 🌸 FLOWER (2)</div>
    <div id="chip3" class="chip" style="display:none;"><span class="dot"></span> 🪣 BUCKET (3)</div>
    <div id="chip4" class="chip" style="display:none;"><span class="dot"></span> 💧 WELL (4)</div>
  </div>

  <!-- Inventory label (shows which item is collected) -->
  <div id="invLabel" class="inventory-label" style="display:none;">
    <span class="ic">📦</span>
    <span>Collected:</span>
    <strong id="invName">—</strong>
  </div>

  <!-- Persistent top inventory bar (2D UI) -->
  <div id="inventoryBar" class="inventory-bar" aria-label="Collected items"></div>

  <div class="foothelp">Tip: Tap the collected item at the top to <b>Use</b> it on what you’re viewing.</div>

  <!-- On-screen verbs -->
  <div class="hud">
    <button id="btn-view" class="btn" aria-label="View"><span class="ic">👁</span><span>View</span></button>
    <button id="btn-use" class="btn" aria-label="Use"><span class="ic">🛠</span><span>Use</span></button>
    <button id="btn-collect" class="btn" aria-label="Collect"><span class="ic">🧺</span><span>Collect</span></button>
  </div>

  <!-- AR Scene -->
  <a-scene
    embedded
    arjs="sourceType: webcam; debugUIEnabled: false;
          detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5;
          sourceWidth:1280; sourceHeight:960; displayWidth:1280; displayHeight:960;
          imageProcessing: { thresholdingBlockSize: 25, thresholdingConstant: -9 }"
    vr-mode-ui="enabled: false"
    renderer="logarithmicDepthBuffer: true"
    wire-up
  >
    <!-- ===== (1) MOM — not collectable, accepts flower/grown_flower ===== -->
    <a-marker type="barcode" value="1" emitevents="true"
              smooth="true" smoothCount="5" smoothTolerance="0.01" smoothThreshold="2">
      <a-entity
        item-meta="
          id: mom;
          view: This is Mom.;
          use: Be nice.;
          collect: You cannot collect Mom.;
          needs_collected: flower, grown_flower;
          use_success: Thank you Darling!;
        ">
        <!-- Placeholder model (2×) — no collectable-model -->
        <a-entity>
          <a-sphere radius="0.28" color="#f7c6c7" position="0 0.30 0"></a-sphere>
          <a-cylinder radius="0.20" height="0.42" color="#f5a8aa" position="0 0.10 0"></a-cylinder>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- ===== (2) FLOWER — grows after watering, then collectable ===== -->
    <a-marker type="barcode" value="2" emitevents="true"
              smooth="true" smoothCount="5" smoothTolerance="0.01" smoothThreshold="2">
      <a-entity
        item-meta="
          id: flower;
          view: This flower needs water.;
          use: Smells nice.;
          collect: false;
          needs_collected: water_bucket;
          use_success: The flower has grown.;
          change: meta2;
        "
        item-meta__meta2="
          id: grown_flower;
          view: A lovely flower.;
          use: Smells nice.;
          collect: true;
          needs_collected: ;
          use_success: You have gathered a flower.;
        ">
        <!-- The thing that gets collected (after grown) -->
        <a-entity class="collectable-model">
          <!-- Variant: small flower (initial) -->
          <a-entity variant="flower">
            <a-cone radius-bottom="0.13" height="0.18" color="#ff66aa" position="0 0.20 0"></a-cone>
            <a-cylinder radius="0.03" height="0.20" color="#4caf50" position="0 0.10 0"></a-cylinder>
          </a-entity>
          <!-- Variant: grown flower (after watering) -->
          <a-entity variant="grown_flower" visible="false">
            <a-cone radius-bottom="0.26" height="0.36" color="#ff66aa" position="0 0.40 0"></a-cone>
            <a-cylinder radius="0.06" height="0.36" color="#4caf50" position="0 0.18 0"></a-cylinder>
          </a-entity>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- ===== (3) BUCKET — becomes water_bucket at the well ===== -->
    <a-marker type="barcode" value="3" emitevents="true"
              smooth="true" smoothCount="5" smoothTolerance="0.01" smoothThreshold="2">
      <a-entity
        item-meta="
          id: bucket;
          view: This is the bucket. You can use it to gather water.;
          use: On what?;
          collect: true;
          needs_collected: ;
          use_success: ;
          change: meta2;
        "
        item-meta__meta2="
          id: water_bucket;
          view: A bucket full of water.;
          use: On what?;
          collect: true;
          needs_collected: ;
          use_success: ;
        ">
        <!-- The thing that gets collected -->
        <a-entity class="collectable-model">
          <!-- Variant: dry bucket -->
          <a-entity variant="bucket">
            <a-cylinder radius="0.24" height="0.32" color="#8a5" position="0 0.16 0"></a-cylinder>
            <a-torus radius="0.28" radius-tubular="0.02" color="#555" position="0 0.32 0" rotation="90 0 0"></a-torus>
          </a-entity>
          <!-- Variant: water bucket (blue top) -->
          <a-entity variant="water_bucket" visible="false">
            <a-cylinder radius="0.24" height="0.32" color="#8a5" position="0 0.16 0"></a-cylinder>
            <a-circle radius="0.22" color="#2aa7ff" position="0 0.32 0" rotation="-90 0 0"></a-circle>
            <a-torus radius="0.28" radius-tubular="0.02" color="#555" position="0 0.32 0" rotation="90 0 0"></a-torus>
          </a-entity>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- ===== (4) WELL — needs bucket; transforms bucket→water_bucket ===== -->
    <a-marker type="barcode" value="4" emitevents="true"
              smooth="true" smoothCount="5" smoothTolerance="0.01" smoothThreshold="2">
      <a-entity
        item-meta="
          id: well;
          view: This is water well.;
          use: I need something to gather the water.;
          collect: It is too big to collect.;
          needs_collected: bucket;
          use_success: You have gathered the water.;
          transform_collected_to: water_bucket;
        ">
        <a-entity>
          <a-cylinder radius="0.36" height="0.32" color="#666" position="0 0.16 0"></a-cylinder>
          <a-box width="0.72" height="0.04" depth="0.72" color="#552" position="0 0.48 0"></a-box>
          <a-cone radius-bottom="0.52" height="0.24" color="#874" position="0 0.60 0" rotation="180 0 0"></a-cone>
        </a-entity>
      </a-entity>
    </a-marker>

    <!-- Camera (inventory anchor added by script) -->
    <a-entity camera></a-entity>
  </a-scene>
</body>
</html>
